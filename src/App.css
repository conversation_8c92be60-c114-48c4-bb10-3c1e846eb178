#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.time-settings {
  display: flex;
  gap: 2em;
  justify-content: center;
  margin-bottom: 2em;
  flex-wrap: wrap;
}

.time-setting {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5em;
}

.time-setting label {
  font-weight: 500;
  font-size: 0.9em;
}

.time-setting input {
  width: 80px;
  padding: 0.5em;
  border: 1px solid #ccc;
  border-radius: 4px;
  text-align: center;
  font-size: 1em;
}

.time-setting input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.session-type {
  text-align: center;
  margin-bottom: 1em;
}

.session-type h3 {
  margin: 0;
  color: #646cff;
  font-size: 1.2em;
}

.timer-display {
  margin-bottom: 1.5em;
}

.timer-display h2 {
  font-size: 4em;
  margin: 0;
  font-family: "Courier New", monospace;
  color: #646cff;
}

.timer-controls {
  display: flex;
  gap: 1em;
  justify-content: center;
  margin-bottom: 1em;
}

.timer-controls button {
  min-width: 80px;
}

.timer-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.read-the-docs {
  color: #888;
}
