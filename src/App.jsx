import { useState, useEffect } from "react";
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";
import "./App.css";

function App() {
  // Timer configuration
  const [workTime, setWorkTime] = useState(25); // Work time in minutes
  const [restTime, setRestTime] = useState(5); // Rest time in minutes
  const [isWorkSession, setIsWorkSession] = useState(true); // Track if it's work or rest

  // Timer state
  const [timeRemaining, setTimeRemaining] = useState(25 * 60); // 25 minutes in seconds
  const [isRunning, setIsRunning] = useState(false);

  // Timer logic
  useEffect(() => {
    let interval = null;

    if (isRunning && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining((time) => time - 1);
      }, 1000);
    } else if (timeRemaining === 0) {
      setIsRunning(false);
      // Auto-switch between work and rest sessions
      setIsWorkSession(!isWorkSession);
      const nextDuration = isWorkSession ? restTime : workTime;
      setTimeRemaining(nextDuration * 60);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRunning, timeRemaining, isWorkSession, workTime, restTime]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Timer controls
  const startTimer = () => setIsRunning(true);
  const stopTimer = () => setIsRunning(false);
  const resetTimer = () => {
    setIsRunning(false);
    setIsWorkSession(true);
    setTimeRemaining(workTime * 60);
  };

  // Handle time setting changes
  const handleWorkTimeChange = (minutes) => {
    setWorkTime(minutes);
    if (isWorkSession && !isRunning) {
      setTimeRemaining(minutes * 60);
    }
  };

  const handleRestTimeChange = (minutes) => {
    setRestTime(minutes);
    if (!isWorkSession && !isRunning) {
      setTimeRemaining(minutes * 60);
    }
  };

  return (
    <>
      <h1>Pomodoro Timer</h1>
      <div className="card">
        {/* Time Settings */}
        <div className="time-settings">
          <div className="time-setting">
            <label htmlFor="work-time">Work Time (minutes):</label>
            <input
              id="work-time"
              type="number"
              min="1"
              max="60"
              value={workTime}
              onChange={(e) =>
                handleWorkTimeChange(parseInt(e.target.value) || 1)
              }
              disabled={isRunning}
            />
          </div>
          <div className="time-setting">
            <label htmlFor="rest-time">Rest Time (minutes):</label>
            <input
              id="rest-time"
              type="number"
              min="1"
              max="30"
              value={restTime}
              onChange={(e) =>
                handleRestTimeChange(parseInt(e.target.value) || 1)
              }
              disabled={isRunning}
            />
          </div>
        </div>

        {/* Session Type Indicator */}
        <div className="session-type">
          <h3>{isWorkSession ? "Work Session" : "Rest Session"}</h3>
        </div>

        {/* Timer Display */}
        <div className="timer-display">
          <h2>{formatTime(timeRemaining)}</h2>
        </div>

        {/* Timer Controls */}
        <div className="timer-controls">
          <button onClick={startTimer} disabled={isRunning}>
            Start
          </button>
          <button onClick={stopTimer} disabled={!isRunning}>
            Stop
          </button>
          <button onClick={resetTimer}>Reset</button>
        </div>

        {/* Status */}
        <p>
          {timeRemaining === 0
            ? "Time's up!"
            : isRunning
            ? "Timer is running..."
            : "Timer is stopped"}
        </p>
      </div>
      <p className="read-the-docs">
        A simple Pomodoro timer to help you stay focused
      </p>
    </>
  );
}

export default App;
